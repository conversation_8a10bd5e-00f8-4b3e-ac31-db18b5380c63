#!/bin/bash

# Build setup script for PickCreator Mobile App
# This script prepares the environment before building

echo "🚀 Setting up PickCreator Mobile build environment..."

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "❌ Error: .env.local file not found!"
    echo "📝 Please copy .env.example to .env.local and fill in your Google OAuth credentials"
    echo "   cp .env.example .env.local"
    exit 1
fi

# Load environment variables
echo "📋 Loading environment variables..."
export $(cat .env.local | grep -v '^#' | xargs)

# Generate google-services.json
echo "🔧 Generating google-services.json..."
./scripts/generate-google-services.sh

if [ $? -eq 0 ]; then
    echo "✅ google-services.json generated successfully!"
else
    echo "❌ Failed to generate google-services.json"
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

echo "✅ Build setup complete! You can now run:"
echo "   flutter run (for debug)"
echo "   flutter build apk (for release APK)"
echo "   flutter build appbundle (for Play Store)"
